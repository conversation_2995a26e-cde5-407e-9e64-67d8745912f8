import axios from 'axios';
import { sendFlashMessage as showFlashMessage } from '../utils/flashMessage';
import AuthService from '../services/authService';

const userStore = {
  namespaced: true,
  state: {
    role: '',
    roles: [],
    permissions: {},
    email: '',
    hasPlusPlan: false,
    hasPremiumPlan: false,
    currentPlan: null,
    isAuthenticated: false,
    isAdmin: false, // Admin flag for admin interface access
    user: null,
    company: null,
    companies: [], // Array of companies user has access to
    // JWT-related state (now the only authentication method)
    jwtToken: null, // Initialize as null - will be populated by authentication flow
    jwtUser: null // User data from JWT authentication
  },
  mutations: {
    setUserRole(state, role) {
      state.role = role;
    },
    setUserRoles(state, roles) {
      state.roles = Array.isArray(roles) ? roles : [roles].filter(Boolean);
    },
    setPermissions(state, permissions) {
      state.permissions = permissions || {};
    },
    setEmail(state, email) {  
      state.email = email;
    },
    setPlanInfo(state, { hasPlusPlan, hasPremiumPlan, currentPlan }) {
      state.hasPlusPlan = hasPlusPlan;
      state.hasPremiumPlan = hasPremiumPlan;
      state.currentPlan = currentPlan;
    },
    setCompany(state, company) {
      state.company = company;
    },
    setCompanies(state, companies) {
      state.companies = companies || [];
    },
    setIsAdmin(state, isAdmin) {
      state.isAdmin = !!isAdmin; // Ensure boolean value
    },
    setAuth(state, { user }) {
      state.user = user;
      state.isAuthenticated = !!user;
    },
    // JWT-specific mutations
    SET_JWT_TOKEN(state, token) {
      console.log('[DEBUG] SET_JWT_TOKEN called with token:', !!token);
      state.jwtToken = token;
      // NOTE: Authorization header is now managed by Axios request interceptor
      // Token is stored only in memory (Vuex state) for XSS protection
    },
    CLEAR_JWT_TOKEN(state) {
      console.log('[DEBUG] CLEAR_JWT_TOKEN called');
      state.jwtToken = null;
      state.jwtUser = null;
      // NOTE: Authorization header clearing is now handled by Axios interceptor
    },
    SET_JWT_USER(state, user) {
      state.jwtUser = user;
      // Update authentication state if we have JWT user
      // TODO: (Future Enhancement - Post Session Removal) Simplify isAuthenticated logic to derive from jwtToken/jwtUser presence
      // Current approach uses both state mutation and getter - could be simplified to getter-only
      if (user) {
        state.isAuthenticated = true;
      }
    },
    setAuthenticated(state, isAuthenticated) {
      state.isAuthenticated = isAuthenticated;
    },
    clearAuth(state) {
      console.log('[DEBUG] clearAuth called - clearing all auth state');
      // Clear JWT auth state  
      state.jwtToken = null;
      state.jwtUser = null;
      state.user = null;
      
      // Clear shared state
      state.isAuthenticated = false;
      state.isAdmin = false;
      state.role = '';
      state.roles = [];
      state.permissions = {};
      state.email = '';
      state.company = null;
      state.companies = [];
      
      // Clear JWT token from memory
      // NOTE: Authorization header clearing is now handled by Axios interceptor
    }
  },
  getters: {
    firstRole: state => state.role,
    hasRole: state => role => state.roles.includes(role),
    hasAnyRole: state => roleList => roleList.some(role => state.roles.includes(role)),
    isOwner: state => state.roles.includes('owner'),
    isAdmin: state => state.roles.includes('admin'),
    isSupervisor: state => state.roles.includes('supervisor'),
    isManager: state => ['owner', 'admin', 'supervisor'].some(r => state.roles.includes(r)),
    
    can: state => permission => !!state.permissions[permission],
    
    hasPlusPlan: state => state.hasPlusPlan,
    hasPremiumPlan: state => state.hasPremiumPlan,
    currentPlan: state => state.currentPlan,
    isAuthenticated: state => state.isAuthenticated,
    currentUser: state => state.user,
    currentCompany: state => state.company,
    userCompanies: state => state.companies,
    companyCount: state => state.companies.length,
    currentRole: state => state.role,
    userEmail: state => state.email,
    // JWT-specific getters
    hasJwtToken: state => !!state.jwtToken,
    jwtToken: state => state.jwtToken,
    jwtUser: state => state.jwtUser,
    // Authentication method getter (JWT-only mode)
    authMethod: state => {
      if (state.jwtToken) return 'jwt';
      return null;
    },
    effectiveUser: state => state.jwtUser || state.user
  },
  actions: {
    async fetchUserData({ commit, state }) {
      try {
        const response = await axios.get('/api/v1/user');
        const { role, roles, permissions, email, has_plus_plan, has_premium_plan, current_plan, company, is_admin } = response.data;

        commit('setUserRole', role);
        commit('setUserRoles', roles || role);
        commit('setPermissions', permissions);
        commit('setEmail', email);
        commit('setIsAdmin', is_admin || false);
        commit('setPlanInfo', {
          hasPlusPlan: has_plus_plan,
          hasPremiumPlan: has_premium_plan,
          currentPlan: current_plan
        });
        if (company) {
          commit('setCompany', company);
        }
        
        // Ensure authenticated state is true after fetching user data
        commit('setAuthenticated', true); // Reinforces auth state post-JWT login/registration
      } catch (error) {
        console.error('Error fetching user data:', error);
        if (error.response?.status === 401 || error.response?.status === 302) {
          console.log('[DEBUG] fetchUserData: 401/302 error - calling clearAuth');
          commit('clearAuth');
        }
      }
    },
    
    async fetchCompanies({ commit }) {
      try {
        const response = await axios.get('/api/v1/companies');
        const companies = response.data.company_user_roles?.map(role => role.company) || [];
        commit('setCompanies', companies);
        return companies;
      } catch (error) {
        console.error('Error fetching companies:', error);
        commit('setCompanies', []);
      }
    },
    
    async login({ commit, dispatch }, credentials) {
      try {
        // Use AuthService for unified login logic (JWT first, session fallback)
        const result = await AuthService.login(credentials);
        
        if (result.success) {
          // AuthService has already updated storage and Vuex state
          // Fetch additional user data (roles, permissions, etc.)
          // TODO: (Future Enhancement - Post JWT Core) Consider consolidating user data fetching
          //       AuthService.attemptJwtLogin() also fetches user data, creating potential redundancy
          //       Single source of truth should be established (likely this fetchUserData action)
          try {
            await dispatch('fetchUserData');
          } catch (fetchError) {
            console.warn('Failed to fetch user data after login:', fetchError);
            // Don't fail the login for this, just log the warning
          }
          
          // Show success message
          showFlashMessage('Přihlášení bylo úspěšné', 'success');
          
          return {
            success: true,
            authMethod: result.authMethod,
            user: result.user
          };
        }
      } catch (error) {
        // AuthService.handleAuthError already shows flash message
        // but we'll show our localized message for consistency
        showFlashMessage('Nesprávný email nebo heslo', 'error');
        throw error;
      }
    },
    
    async logout({ commit }) {
      try {
        // Use AuthService for unified logout logic
        const result = await AuthService.logout();
        
        if (!result.success) {
          console.warn('Logout may have partially failed:', {
            jwtLogoutSuccess: result.jwtLogoutSuccess,
            sessionLogoutSuccess: result.sessionLogoutSuccess
          });
        }
        
        // AuthService has already cleared local auth state
        
        // CRITICAL: Clear service worker caches on logout to prevent stale data
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          console.log('[Auth] Posting CLEAR_API_CACHES message to service worker');
          navigator.serviceWorker.controller.postMessage({ type: 'CLEAR_API_CACHES' });
        }
        
        // Show success message
        showFlashMessage('Odhlášení bylo úspěšné', 'success');
      } catch (error) {
        console.error('Logout error:', error);
        // Even if logout fails, clear local state for security
        commit('clearAuth');
        
        // CRITICAL: Clear service worker caches even on error
        if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
          console.log('[Auth] Posting CLEAR_API_CACHES message to service worker (error path)');
          navigator.serviceWorker.controller.postMessage({ type: 'CLEAR_API_CACHES' });
        }
        
        showFlashMessage('Odhlášení bylo úspěšné', 'success');
      }
    },
    
    
    async register({ commit, dispatch }, userData) {
      try {
        // Use AuthService for JWT registration
        const result = await AuthService.register(userData);
        
        if (result.success) {
          // JWT registration automatically logs the user in
          // AuthService.register has already:
          // - Created the user account
          // - Generated JWT tokens
          // - Stored access token and set HttpOnly refresh cookie
          // - Updated Vuex state with JWT authentication
          // - Set jwtToken and jwtUser in store
          
          // Fetch additional user data (roles, permissions, company info)
          // This provides detailed user info beyond what jwt_register returns
          try {
            await dispatch('fetchUserData');
          } catch (fetchError) {
            console.warn('Failed to fetch detailed user data after registration:', fetchError);
            // Don't fail the registration for this, user is still logged in with basic info
          }
          
          // Show success message (AuthService.register already sets message)
          showFlashMessage(result.message || 'Registrace byla úspěšná. Nyní jste přihlášeni.', 'success');
          
          return result;
        }
      } catch (error) {
        // Handle registration errors with localized messages
        if (error.response?.data?.errors) {
          const errors = error.response.data.errors;
          const errorMessages = Object.keys(errors).map(key => `${key}: ${errors[key].join(', ')}`).join('; ');
          showFlashMessage(errorMessages, 'error');
        } else {
          showFlashMessage('Registrace selhala', 'error');
        }
        throw error;
      }
    }
  }
};

export default userStore;