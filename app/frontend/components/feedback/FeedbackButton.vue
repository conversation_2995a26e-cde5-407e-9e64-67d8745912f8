<template>
  <div class="feedback-footer">
    <button class="feedback-btn" @click="openFeedback" title="Pomozte nám zlepšit Týmbox">
      <span class="feedback-text">
        🖐
        {{ $t('feedback.short', 'Kontakt') }}
      </span>
    </button>
  </div>
</template>

<script>
import { Hand } from 'lucide-vue-next';
export default {
  components: { Hand },
  name: 'FeedbackButton',
  props: {
    pageUrl: { type: String, default: '' }
  },
  methods: {
    openFeedback() {
      const url = this.pageUrl || window.location.pathname
      const event = new CustomEvent('open-central-modal', {
        detail: {
          componentName: 'FeedbackForm',
          title: this.$t('feedback.title', 'Kontakt přímo k nám'),
          props: { pageUrl: url }
        }
      })
      document.dispatchEvent(event)
    }
  }
}
</script>

<style scoped>
.feedback-footer { 
  display: inline-block;
}
.feedback-btn {
  font-size: 0.75rem; 
  color: #9ca3af;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  padding: 6px 12px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.feedback-btn:hover { 
  color: #2563eb;
  background: #ffffff;
  border-color: #2563eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.feedback-text {
  font-weight: 500;
}
</style>

